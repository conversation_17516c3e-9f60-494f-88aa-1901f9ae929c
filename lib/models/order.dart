import 'package:flutter/material.dart';

enum OrderStatus { pending, processing, shipped, delivered, cancelled }

enum HandlingStatus { assigned, inProgress, completed, onHold }

class Order {
  final String id;
  final String customerName;
  final String customerPhone;
  final double totalAmount;
  final DateTime assignedDate;
  final String assignee;
  final OrderStatus status;
  final HandlingStatus handlingStatus;
  final String address;
  final int itemsCount;

  Order({
    required this.id,
    required this.customerName,
    required this.customerPhone,
    required this.totalAmount,
    required this.assignedDate,
    required this.assignee,
    required this.status,
    required this.handlingStatus,
    required this.address,
    required this.itemsCount,
  });
}
